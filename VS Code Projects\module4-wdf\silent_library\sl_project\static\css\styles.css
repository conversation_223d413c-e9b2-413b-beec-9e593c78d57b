/* ===========================
  Base Styles
  =========================== */
body {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
}

html,
body {
  margin: 0;
  padding: 0;
}

/* ===========================
  Typography
  =========================== */
h1,
h2,
h3 {
  font-family: 'Merriweather', serif;
  font-weight: bold;
}

h1 {
  font-size: 32px;
}

h2 {
  font-size: 24px;
}

h3 {
  font-size: 20px;
}

section h2 {
  margin-top: 0 !important;
}

.caption {
  font-size: 14px;
}

/* ===========================
  Links & Buttons
  =========================== */
a {
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #ffc107;
}

.btn-primary {
  background-color: #1D3557;
  border: none;
}

.btn-primary:hover {
  background-color: #16324F;
}

.cta-button {
  background-color: #F1FAEE;
  color: #1D3557;
  border: none;
  padding: 15px 30px;
  font-size: 1em;
  font-weight: 500;
  border-radius: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #e6f0ec;
}

/* ===========================
  Carousel Styles
  =========================== */
.carousel-item {
  background-color: #d1dff2;
  color: white;
  padding: 10px 0;
}

.carousel-item .btn {
  background-color: #1D3557;
  color: #ffffff;
}

.carousel-item .btn:hover {
  background-color: #7ca2d8;
}

/* Carousel Prev/Next Buttons */
.carousel-control-prev,
.carousel-control-next {
  width: 5%;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  filter: invert(0%) brightness(0%) contrast(200%);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 4rem;
  height: 4rem;
  background-size: 100% 100%;
}

@media (max-width: 991.98px) {

  .carousel-control-prev,
  .carousel-control-next {
    top: auto;
    bottom: 10px;
    transform: none;
  }
}

.carousel-control-prev {
  left: 30px;
}

.carousel-control-next {
  right: 30px;
}


/* Base style: hidden by default */
.fade-arrow {
  opacity: 0;
  transition: opacity 0.1s ease;
}

/* Show arrows only when hovering over carousel */
#silentLibraryCarousel:hover .fade-arrow {
  opacity: 1;
}

/* Optional: shrink width for tighter control position */
.carousel-nav {
  width: 4%;
}


/* ===========================
  Banner Styles
  =========================== */
.banner {
  background-color: #1D3557;
  color: white;
  padding: 60px 30px;
  text-align: center;
}

.banner h1 {
  font-size: 3em;
  margin-bottom: 10px;
}

.banner p {
  font-size: 1.2em;
  color: #d1dbe9;
  margin-bottom: 30px;
}

.banner .cta-button {
  background-color: #F1FAEE;
  color: #1D3557;
  border: none;
  padding: 15px 30px;
  font-size: 1em;
  font-weight: 500;
  border-radius: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.banner .cta-button:hover {
  background-color: #e6f0ec;
}


/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1030;
  /* keep header above other content */
  background-color: white;
  /* ensure it’s visible */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



main#content-area {
  padding-top: 500px;
  /* adjust this to match your header height */
}

/* ===========================
  Navbar Styles
  =========================== */
.navbar-custom {
  background-color: #0F2E5A;
}

.navbar-custom .nav-link,
.navbar-custom .navbar-brand {
  color: white;
}

.navbar-custom .navbar-nav .nav-link.active,
.navbar-nav .nav-link:hover {
  color: #d9d6f8;
  font-weight: bold;
  font-size: 1.3em;
  text-decoration: none;
}

#navbarNav.collapsing {
  height: auto !important;
}

/* ===========================
  See All link  Styles
  ===========================   */
.see-all-link {
  color: #000;
  /* dark black */
  font-weight: 700;
  /* bold */
  font-size: 1.25rem;
  /* larger text */
  text-transform: capitalize;
  letter-spacing: 1.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  transition: color 0.3s ease;
}

.see-all-link:hover {
  color: #0056b3;
  /* darker blue on hover */
  text-shadow: 2px 2px 4px rgba(0, 86, 179, 0.4);
  cursor: pointer;
}


/* ===========================
  Card & Image Styles
  =========================== */
/* Homepage books images */
.card-img-top {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.card[data-page] {
  cursor: pointer;
}

/* Homepage Explore more section */
.explore-img {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.explore-card {
  height: 100%;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: start;
}

/* Books.html thumbnails */
.book-thumbnail {
  width: 100%;
  height: 220px;
  object-fit: cover;
  border-radius: 4px;
}

.book-row {
  display: flex;
  overflow-x: auto;
  gap: 1rem;
  padding-bottom: 1rem;
}

.book-card {
  min-width: 190px;
  flex: 0 0 auto;
}

.book-card img {
  height: 220px;
  object-fit: cover;
}

.btn-disabled {
  pointer-events: none;
  opacity: 0.6;
}

/* ===========================
  Events Section
  =========================== */
.events-img-wrapper {
  height: 180px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.events-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.events-card {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  text-align: center;
  height: 100%;
}

/* ===========================
  Events highlight section
  =========================== */
#highlight-section {
  min-height: 200px;
}

/* ===========================
  Trendng & Popular Books  section
  =========================== */
#trending-section {
  min-height: 200px;
}

/* ===========================
  Search Bar
  =========================== */
#searchBar {
  opacity: 0;
  transform: scaleY(0);
  transform-origin: top;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

#searchBar.show {
  display: block !important;
  opacity: 1;
  transform: scaleY(1);
}

/* ===========================
  Footer
  =========================== */
footer {
  margin-top: auto;
}

.footer-touch-link {
  display: inline-block;
  padding: 2px 8px;
  margin-bottom: 2px;
  font-size: 1rem;
  min-width: 48px;
  min-height: 28px;
}

/* ===========================
  Add Pointer to all [data-page] elements
  ===========================  */
[data-page] {
  cursor: pointer;
}